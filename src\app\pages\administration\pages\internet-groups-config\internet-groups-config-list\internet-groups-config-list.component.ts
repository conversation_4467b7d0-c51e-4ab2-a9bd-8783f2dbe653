import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { PermissionActions } from 'src/app/@shared/models';
import { InternetGroupsConfigService } from '../internet-groups-config.service';
import { InternetGroup } from '../models/internet-groups-config.model';

@Component({
  selector: 'app-internet-groups-config-list',
  templateUrl: './internet-groups-config-list.component.html',
  styleUrls: ['./internet-groups-config-list.component.scss']
})
export class InternetGroupsConfigListComponent extends BaseComponent implements OnInit {

  internetGroups!: Array<InternetGroup>;
  showCreateModal = false;
  selectedInternetGroup!: InternetGroup | null;
  permissionActions = PermissionActions;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.INTERNET_GROUPS;

  constructor(
    private readonly internetGroupsConfigService: InternetGroupsConfigService,
    private readonly cdf: ChangeDetectorRef,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService,
    private readonly activeRoute: ActivatedRoute
  ) {
    super();
    this.pageTitle = 'Internet Groups Config';
  }

  ngOnInit(): void {
    this.getAll();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getInternetGroupById(Number(params.id));
        }
      });
  }

  getInternetGroupById(id: number) {
      this.internetGroupsConfigService.get<InternetGroup>(id).pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          this.onAddEditInternetGroup(res);
        }
      });
    }

  getAll(): void {
    this.isLoading = true;
    this.internetGroupsConfigService.getList<InternetGroup>()
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          this.internetGroups = res;
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onAddEditInternetGroup(internetGroup?: InternetGroup): void {
    if (internetGroup?.id) {
      this.selectedInternetGroup = internetGroup;
    }
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  showDeleteModal(internetGroup: InternetGroup, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'internet group'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteInternetGroup(internetGroup);
      }
    });
  }

  onDeleteInternetGroup(internetGroup: InternetGroup): void {
    this.internetGroupsConfigService.delete(internetGroup?.id)
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          this.getAll();
          this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('{item}', 'Internet Group'));
        }
      });
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }

  onClose(bool: boolean): void {
    this.showCreateModal = false;
    this.selectedInternetGroup = null;
    this.cdf.detectChanges();
    if (bool) {
      this.getAll();
    }
  }
}
