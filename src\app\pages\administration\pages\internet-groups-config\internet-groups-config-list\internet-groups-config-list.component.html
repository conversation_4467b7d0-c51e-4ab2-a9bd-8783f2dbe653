<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      (click)="onAddEditInternetGroup()"
      *appHasPermission="[permissionActions.CREATE_INVENTORY]"
    >
      <span class="role-label">Add New Internet Group</span>
    </button>
  </div>
</app-page-header>

<ng-container *ngIf="!isLoading; else pageLoaderTemplate">
  <p-table
    class="no-column-selection internet-groups-table"
    [scrollable]="true"
    [value]="internetGroups"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="false"
    [rowHover]="true"
    [loading]="isLoading"
    styleClass="p-datatable-gridlines"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <th pResizableColumn scope="col">Name</th>
        <th pResizableColumn scope="col" *appHasPermission="[permissionActions.UPDATE_INVENTORY, permissionActions.DELETE_INVENTORY]">Actions</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-internetGroup>
      <tr>
        <td>
          {{ internetGroup?.name }}
        </td>
        <td class="actions" *appHasPermission="[permissionActions.UPDATE_INVENTORY, permissionActions.DELETE_INVENTORY]">
          <img [src]="constants.staticImages.icons.edit" (click)="onAddEditInternetGroup(internetGroup)" alt="Edit" *appHasPermission="[permissionActions.UPDATE_INVENTORY]" />
          <img
            [src]="constants.staticImages.icons.deleteIcon"
            (click)="showDeleteModal(internetGroup, $event)"
            alt="Delete"
            *appHasPermission="[permissionActions.DELETE_INVENTORY]"
          />
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="6" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
</ng-container>

<ng-template #pageLoaderTemplate>
  <div class="no-data mt-5">
    <em class="pi pi-spin pi-spinner"></em>
  </div>
</ng-template>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-internet-groups-config-add-update
    *ngIf="showCreateModal"
    [selectedInternetGroup]="selectedInternetGroup"
    (onClose)="onClose($event)"
  ></app-internet-groups-config-add-update>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>

<p-confirmPopup></p-confirmPopup>
