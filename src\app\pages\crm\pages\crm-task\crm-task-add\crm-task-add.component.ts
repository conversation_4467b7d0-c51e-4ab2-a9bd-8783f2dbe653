import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Constants, MESSAGES, VALIDATION_CONSTANTS, dateFormat, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { allowExtensions, getRefactorFileName } from '@core/utils/fileUpaloadName.util';
import { ShopUser } from '@pages/administration/models';
import { ContactDetails } from '@pages/administration/models/crm.model';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { CustomerLeadListFilter, CustomerLeadListItem } from '@pages/crm/models/customer-lead.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { ActivityList, StockBasicInfo, TaskAttachment, TaskCreateParams, TaskListItem } from '@pages/shops/models';
import { TaskService } from '@pages/shops/services/tasks.service';
import { User } from '@pages/user/models/user.model';
import * as saveAs from 'file-saver';
import { ConfirmationService } from 'primeng/api';
import { DropdownChangeEvent } from 'primeng/dropdown';
import { takeUntil } from 'rxjs';
import { FileProperties, FileUploadProgress, IdNameModel } from 'src/app/@shared/models';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-crm-task-add',
  templateUrl: './crm-task-add.component.html',
  styleUrls: ['./crm-task-add.component.scss']
})
export class CrmTaskAddComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Task';
  taskFormGroup!: FormGroup;
  hasDataBeenModified = false;
  isEditMode = false;
  taskTypes: IdNameModel[] = [];
  taskStatuses: IdNameModel[] = [];
  shops: IdNameModel[] = [];
  stockBasicInfo: StockBasicInfo[] = [];
  assignees: ShopUser[] = [];
  currentUser!: Account | null;
  showCreateActivityModal = false;
  fetchNewActivities = false;
  fileUploadProgresses: FileUploadProgress[] = [];
  taskFileUploadPath = 'Tasks Files';
  isUploadingFile = false;
  showAttachmentsTab = true;
  showEditBtn = false;
  activityList = ActivityList;
  loaders = {
    taskTypes: false,
    taskStatuses: false,
    stockNumbers: false,
    shopUsers: false,
    previousOwnerName: false,
    customerLead: false
  }

  accordionTabs = {
    details: true,
    description: false,
    activity: true,
    attachments: true,
  }
  contactList: ContactDetails[] = [];
  @Input() taskInfo!: TaskListItem | null;
  @Input() taskId!: string | undefined;
  @Input() isViewMode!: boolean | null;
  @Input() activeIndex!: number;
  @Input() crmCustomerInfoId!: string | undefined;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input() filterParams: CustomerLeadListFilter = new CustomerLeadListFilter();
  customerLeadList: CustomerLeadListItem[] = [];
  users: User[] = [];
  isActiveTab = true;
  showCreateContact = false;
  maxFileSizeAllowed = MESSAGES.maxFileSizeAllowed;
  selectedCommentId!: number;
  showEnlargedImage = false;
  viewImageScr?: string;

  constructor(
    private readonly fb: FormBuilder,
    private readonly taskService: TaskService,
    private readonly toasterService: AppToasterService,
    private readonly authService: AuthService,
    private readonly fileUploadService: FileUploadService,
    private readonly confirmationService: ConfirmationService,
    private readonly crmService: CrmService,
    private readonly cdf: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly activatedRoute: ActivatedRoute,
    private readonly userAnnotationService: UserAnnotationService,

    private readonly commonSharedService: CommonSharedService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.commonSharedService.setBlockUI$(true);
    this.initializeFormGroup();
    this.getTaskInfo(this.taskId);
    this.getCurrentUser();
    if (this.isViewMode) {
      this.hideShowAttachmentsTab();
    }
    this.getAllMasterData();
    this.activatedRoute.queryParams
      .subscribe(params => {
        if (params?.commentId) {
          this.selectedCommentId = Number(params?.commentId);
        }
      })

  }

  private async getAllMasterData() {
    await Promise.all([
      this.getTaskTypes(),
      this.getTaskStatuses(),
      this.getStockNumbers(),
      this.getContactInfo(),
      this.getAllCustomerLead(),
      this.getAllAssignee()
    ]);
    this.commonSharedService.setBlockUI$(false);
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user && this.taskFormGroup) {
        this.currentUser = user;
        if (!this.isEditMode) {
          const reporterControl = this.taskFormGroup.get('reporterName');
          this.taskFormGroup.get('reporterId')?.setValue(user.id);
          reporterControl?.setValue(this.utils.getFullName(user.firstName, user.lastName));
          reporterControl?.disable();
        }
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.taskId?.currentValue && !changes.isViewMode?.currentValue) {
      this.title = 'Edit Task';
      this.isEditMode = true;
    }
    if (changes.taskId?.currentValue && changes.isViewMode?.currentValue) {
      this.title = 'View Task';
    }
  }

  private getTaskInfo(taskId: string | undefined): void {
    if (taskId) {
      this.taskService.get<TaskListItem>(taskId).pipe(takeUntil(this.destroy$)).subscribe({
        next: task => {
          this.taskInfo = task;
          this.showEditBtn = !task?.archived;
          this.setTaskInfoInFormGroup();
          if (this.isViewMode) {
            this.hideShowAttachmentsTab();
          }
        },
        error: () => {
        }
      })
    }
  }

  private initializeFormGroup(): void {
    this.taskFormGroup = this.fb.group({
      id: new FormControl(null),
      taskTypeId: new FormControl(null, [Validators.required]),
      taskStatusId: new FormControl(null, [Validators.required]),
      stockNumber: new FormControl(null),
      assigneeId: new FormControl(null, [Validators.required]),
      reporterId: new FormControl(null, [Validators.required]),
      reporterName: new FormControl(null),
      summary: new FormControl(null, [Validators.required, Validators.maxLength(VALIDATION_CONSTANTS.inputMaxLength)]),
      description: new FormControl(null, [Validators.maxLength(VALIDATION_CONSTANTS.descriptionMaxLength)]),
      startDate: new FormControl(null),
      endDate: new FormControl(null),
      pipelineId: new FormControl(),
      pipelineSequence: new FormControl(),
      crmContactId: new FormControl(null),
      activity: new FormControl(null, [Validators.required]),
      customerLeadId: new FormControl(null),
    });
    this.taskFormGroup.get('taskTypeId')?.disable();
    if (this.crmCustomerInfoId) {
      this.taskFormGroup.get('customerLeadId')?.patchValue(this.crmCustomerInfoId);
    }
  }

  get taskInfoCreateParams(): TaskCreateParams {
    const date2 = this.taskFormGroup.controls['endDate'].value
    let edDate: any = null;
    if (date2) {
      edDate = this.datePipe.transform(date2, dateFormat.format);
      edDate = new Date(edDate);
    }

    return {
      ...this.taskFormGroup.getRawValue(),
      id: this.taskInfo?.id,
      taskAttachments: this.taskAttachments,
      unitId: this.selectedUnitId,
      phaseId: this.taskInfo?.phaseId,
      pipelineId: this.taskInfo?.pipelineId,
      endDate: edDate
    };
  }

  get taskAttachments(): TaskAttachment[] {
    let taskAttachments: TaskAttachment[] = this.fileUploadProgresses.map(fileProgress => ({ url: fileProgress.uploadedFileUrl }));
    if (this.isEditMode && this.taskInfo?.taskAttachments?.length) {
      taskAttachments = [...this.taskInfo?.taskAttachments, ...taskAttachments];
    }
    return taskAttachments;
  }

  get selectedUnitId(): number | undefined {
    const stockNumber = this.taskFormGroup.getRawValue().stockNumber;
    return this.stockBasicInfo.find(stock => stock.stockNumber === stockNumber)?.unitId;
  }

  get isFileUploadInProgress(): boolean {
    if (this.fileUploadProgresses.some(fileProgress => fileProgress.progress$.getValue() < 100)) {
      return true;
    }
    return false;
  }

  onSubmit(close = true): void {
    if (this.isViewMode) {
      this.taskFormGroup.enable();
      this.isViewMode = false;
      const reporterControl = this.taskFormGroup.get('reporterName');
      reporterControl?.disable();
      this.taskFormGroup.get('stockNumber')?.disable();
      this.taskFormGroup.get('assigneeId')?.disable();
      this.taskFormGroup.get('taskTypeId')?.disable();
      return;
    }
    if (this.taskFormGroup.invalid) {
      this.taskFormGroup.markAllAsTouched();
      return;
    }
    if (this.isFileUploadInProgress) {
      this.toasterService.warning(MESSAGES.fileUploadInProgress);
      return;
    }
    this.taskFormGroup.get('startDate')?.setValue(this.taskFormGroup.controls.endDate.value);
    if (this.isEditMode) {
      this.editTask();
    } else {
      this.saveTask(close);
    }
  }

  saveTask(close = true): void {
    this.taskService.add(this.taskInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.shopTaskUpdateSuccess : MESSAGES.shopTaskAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.taskFormGroup.reset();
        this.getCurrentUser();
        if (!this.isEditMode) {
          for (const taskType of this.taskTypes) {
            if (taskType.name === 'Sales') {
              this.taskFormGroup.get('taskTypeId')?.setValue(taskType.id);
            }
          }
          for (const taskStatuse of this.taskStatuses) {
            if (taskStatuse.name === 'To Do') {
              this.taskFormGroup.patchValue({ taskStatusId: taskStatuse.id });
            }
          }
          this.taskFormGroup.get('reporterId')?.setValue(this.currentUser?.id);
          this.fileUploadProgresses = [];
        }
      }
    });
  }

  editTask(): void {
    this.taskService.update(this.taskInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.shopTaskUpdateSuccess : MESSAGES.shopTaskAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  editForm() {
    this.showAttachmentsTab = true;
    this.title = 'Edit Task';
    this.isEditMode = true;
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  async setTaskInfoInFormGroup(): Promise<void> {
    if (this.taskInfo) {
      this.taskFormGroup.patchValue({
        taskTypeId: this.taskInfo?.taskType?.id,
        taskStatusId: this.taskInfo?.taskStatus?.id,
        summary: this.taskInfo?.summary,
        description: this.taskInfo?.description,
        unitId: this.taskInfo?.unitId,
        stockNumber: this.taskInfo?.stockNumber,
        startDate: this.taskInfo?.startDate ? new Date(`${this.taskInfo?.startDate}`) : '',
        endDate: this.taskInfo?.endDate ? new Date(`${this.taskInfo?.endDate}`) : '',
        crmContactId: this.taskInfo?.crmContact?.id,
        customerLeadId: this.taskInfo?.customerLeadId,
        activity: this.taskInfo?.activity,
        assigneeId: this.taskInfo?.assignee?.id,
        reporterId: this.taskInfo?.reporter?.id,
        reporterName: this.taskInfo?.reporter?.name,
      });
      this.taskFormGroup.get('reporterName')?.disable();
      if (this.isEditMode) {
        this.taskFormGroup.get('stockNumber')?.disable();
        this.taskFormGroup.get('assigneeId')?.disable();
      }
      if (this.isViewMode) {
        this.taskFormGroup.disable();
      }
    }
  }

  private async getTaskTypes(): Promise<void> {
    this.loaders.taskTypes = true;
    this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.types).pipe(takeUntil(this.destroy$)).subscribe({
      next: (taskTypes) => {
        this.taskTypes = taskTypes;
        this.loaders.taskTypes = false;
        if (!this.isEditMode) {
          for (const taskType of this.taskTypes) {
            if (taskType.name === 'Sales') {
              this.taskFormGroup.get('taskTypeId')?.setValue(taskType.id);
            }
          }
        }
      },
      error: () => {
        this.loaders.taskTypes = false;
      }
    });
  }

  private async getTaskStatuses(): Promise<void> {
    this.loaders.taskStatuses = true;
    this.taskService.getList<IdNameModel>(API_URL_UTIL.tasks.statuses).pipe(takeUntil(this.destroy$)).subscribe({
      next: (taskStatuses) => {
        this.taskStatuses = taskStatuses;
        if (!this.isEditMode && !this.isViewMode) {
          for (const taskStatuse of this.taskStatuses) {
            if (taskStatuse.name === 'To Do') {
              this.taskFormGroup.patchValue({ taskStatusId: taskStatuse.id });
            }
          }
        }
        this.loaders.taskStatuses = false;
      },
      error: () => {
        this.loaders.taskStatuses = false;
      }
    });
  }

  private async getContactInfo(): Promise<void> {
    this.loaders.previousOwnerName = true;
    this.crmService.getList<ContactDetails>(API_URL_UTIL.admin.crm.contact).pipe(takeUntil(this.destroy$)).subscribe({
      next: (contactList) => {
        this.contactList = contactList;
        this.loaders.previousOwnerName = false;

        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.previousOwnerName = false;
        this.cdf.detectChanges();
      }
    });
  }

  private async getStockNumbers(): Promise<void> {
    this.loaders.stockNumbers = true;
    this.taskService.getList<StockBasicInfo>(API_URL_UTIL.tasks.stockNumbers).pipe(takeUntil(this.destroy$)).subscribe({
      next: (stockNumbers) => {
        this.stockBasicInfo = stockNumbers;
        this.loaders.stockNumbers = false;
      },
      error: () => {
        this.loaders.stockNumbers = false;
      }
    });
  }

  onFileSelect(event: any) {
    for (const file of event.target.files) {
      if (file.size > this.constants.fileSize) {
        this.toasterService.warning(MESSAGES.fileUploadMessage)
      }
      else {
        if (event.target?.files?.length) {
          const modifiedFileName = getRefactorFileName(file.name);
          const isExtensionSupported = allowExtensions(modifiedFileName,Constants.allowedImgFormats)
          if (isExtensionSupported) {
            const modifiedFile = new File([file], modifiedFileName, { type: file.type });
            this.uploadImage(modifiedFile);
          } else {
            this.toasterService.error(MESSAGES.fileTypeNotSupported);
            return
          }
        }
      }
    }
  }

  uploadImage(file: File): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, this.taskFileUploadPath, this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.fileUploadSuccess);
        },
        error: err => {
          this.toasterService.error(MESSAGES.fileUploadError);
        }
      })
  }

  downloadImage(taskAttachment: TaskAttachment): void {
    if (taskAttachment?.url) {
      this.fileUploadService.downloadFile(taskAttachment.url, this.getFileName(taskAttachment.url)).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/image" }), this.getFileName(taskAttachment.url));
        }
      });
    }
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  deleteImageFromCloud(imageUrl: string, fileIndex: number, callbackFn: Function): void {
    this.fileUploadService.deleteFile([imageUrl]).pipe(takeUntil(this.destroy$)).subscribe(() => {
      callbackFn(fileIndex);
    })
  }

  removeFileFromUpload(fileIndex: number): void {
    const spliceImageFunction = () => {
      this.fileUploadProgresses.splice(fileIndex, 1);
    }
    const fileProgress = this.fileUploadProgresses[fileIndex];
    if (fileProgress.uploadedFileUrl) {
      this.deleteImageFromCloud(fileProgress.uploadedFileUrl, fileIndex, spliceImageFunction);
    }
  }

  onDelete(attachmentId: number | undefined, event: Event): void {
    if (!attachmentId) {
      return;
    }
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'attachment'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(attachmentId);
      }
    });
  }

  onDeleteConfirmation(attachmentId: number): void {
    this.taskService.deleteTaskAttachment(attachmentId).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.photoDeleteSuccess);
      const index = this.taskInfo?.taskAttachments.findIndex(attachment => attachment.id === attachmentId);
      if (index !== undefined && index > -1) {
        this.taskInfo?.taskAttachments.splice(index, 1);
      }
    });
  }

  getFileName(fileUrl: string | undefined): string {
    if (!fileUrl) {
      return '';
    }
    return fileUrl.split('_').slice(1).join('_');
  }

  hideShowAttachmentsTab() {
    this.showAttachmentsTab = !this.taskInfo?.taskAttachments.length ? false : true;
  }

  openContactModel(): void {
    this.showCreateContact = true
  }

  private async getAllCustomerLead(): Promise<void> {
    this.isLoading = true;
    this.loaders.customerLead = true;
    this.filterParams.orderBy = this.orderBy;
    this.crmService.getListWithFiltersWithPagination<CustomerLeadListFilter, CustomerLeadListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.crm.customerFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.customerLeadList = res.content;
          this.setPaginationParamsFromPageResponse<CustomerLeadListItem>(res);
          this.isLoading = false;
          this.loaders.customerLead = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  private async getAllAssignee(): Promise<void> {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.loaders.shopUsers = true;
    this.userAnnotationService.getList<User>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.users = res;
          this.isLoading = false;
          this.loaders.shopUsers = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateContact = false;
    if (refreshList) {
      this.getContactInfo();
    }
  }

  onViewImage(fileUrl: string | undefined) {
    this.showEnlargedImage = true;
    if (fileUrl) {
      this.viewImageScr = fileUrl;
    }
  }
  onChangeContact(event?: DropdownChangeEvent): void {
      if (event) {
        const selectedContact: ContactDetails = this.contactList.find(contact => contact.id === event.value);
        if (selectedContact && selectedContact.archived) {
          this.taskFormGroup.controls['crmContactId'].setValue(null);
          this.toasterService.warning(MESSAGES.archivedContact);
          return;
        }
      }
      this.cdf.detectChanges(); 
  }
  
  isOptionDisabled(option: { archived: boolean; id: number }): boolean {
    const selectedValue = this.taskFormGroup.get('crmContactId')?.value as number;
    return option.archived && option.id !== selectedValue;
  }
}
