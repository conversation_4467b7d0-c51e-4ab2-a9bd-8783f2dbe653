<div class="image-zoom-overlay" *ngIf="isVisible">
  <button
    class="zoom-close-btn"
    (click)="closeOverlay()"
    title="Close (Esc)"
    [attr.aria-label]="'Close image viewer. Press Escape key to close.'"
  >
    <fa-icon [icon]="faIcons.faTimes"></fa-icon>
  </button>

  <div class="image-counter" title="Image position in gallery">
    {{ selectedIndex + 1 }} / {{ images.length }}
  </div>

  <button
    class="nav-arrow nav-arrow-left"
    (click)="navigateImage('prev')"
    [disabled]="selectedIndex === 0"
    title="Previous image (← Arrow key)"
    [attr.aria-label]="'Go to previous image. Use left arrow key or click.'"
  >
    <fa-icon [icon]="faIcons.faArrowLeft"></fa-icon>
  </button>

  <button
    class="nav-arrow nav-arrow-right"
    (click)="navigateImage('next')"
    [disabled]="selectedIndex === images.length - 1"
    title="Next image (→ Arrow key)"
    [attr.aria-label]="'Go to next image. Use right arrow key or click.'"
  >
    <fa-icon [icon]="faIcons.faArrowDown"></fa-icon>
  </button>

  <div class="zoom-controls">
    <button
      class="zoom-btn"
      (click)="zoomIn()"
      [disabled]="zoomLevel >= maxZoom"
      title="Zoom in (+)"
      [attr.aria-label]="'Zoom in. Press + key or scroll up to zoom in.'"
    >
      <fa-icon [icon]="faIcons.faAdd"></fa-icon>
    </button>
    <span
      class="zoom-level"
      title="Current zoom level"
      [attr.aria-label]="'Current zoom level: ' + Math.round(zoomLevel * 100) + ' percent'"
    >
      {{ Math.round(zoomLevel * 100) }}%
    </span>
    <button
      class="zoom-btn"
      (click)="zoomOut()"
      [disabled]="zoomLevel <= minZoom"
      title="Zoom out (-)"
      [attr.aria-label]="'Zoom out. Press - key or scroll down to zoom out.'"
    >
      <fa-icon [icon]="faIcons.faMinus"></fa-icon>
    </button>
    <button
      class="zoom-btn"
      (click)="resetZoom()"
      title="Reset zoom (0)"
      [attr.aria-label]="'Reset zoom to 100%. Press 0 key to reset zoom.'"
    >
      <fa-icon [icon]="faIcons.faRefresh"></fa-icon>
    </button>
  </div>

  <!-- Help Button -->
  <button
    class="help-btn"
    (click)="toggleHelp()"
    title="Show keyboard shortcuts and features"
    [attr.aria-label]="'Show help with keyboard shortcuts and zoom features'"
  >
    <fa-icon [icon]="faIcons.faInfo"></fa-icon>
  </button>

  <!-- Help Overlay -->
  <div class="help-overlay" *ngIf="showHelp" (click)="toggleHelp()">
    <div class="help-content" (click)="$event.stopPropagation()">
      <div class="help-header">
        <h3>Image Zoom Features</h3>
        <button class="help-close" (click)="toggleHelp()" title="Close help">
          <fa-icon [icon]="faIcons.faTimes"></fa-icon>
        </button>
      </div>
      <div class="help-sections">
        <div class="help-section">
          <h4>🔍 Zoom Controls</h4>
          <ul>
            <li><strong>Mouse Wheel:</strong> Scroll to zoom in/out</li>
            <li><strong>+ Key:</strong> Zoom in</li>
            <li><strong>- Key:</strong> Zoom out</li>
            <li><strong>0 Key:</strong> Reset zoom to 100%</li>
            <li><strong>Click buttons:</strong> Use zoom controls at bottom</li>
          </ul>
        </div>
        <div class="help-section">
          <h4>🖱️ Pan & Navigate</h4>
          <ul>
            <li><strong>Click & Drag:</strong> Pan around when zoomed in</li>
            <li><strong>← → Arrow Keys:</strong> Navigate between images</li>
            <li><strong>Click arrows:</strong> Use navigation buttons</li>
          </ul>
        </div>
        <div class="help-section">
          <h4>⌨️ Keyboard Shortcuts</h4>
          <ul>
            <li><strong>Esc:</strong> Close image viewer</li>
            <li><strong>← →:</strong> Previous/Next image</li>
            <li><strong>+ - 0:</strong> Zoom controls</li>
            <li><strong>? or H:</strong> Toggle this help</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <div
    class="zoom-image-container"
    [class.can-pan]="zoomLevel > 1"
    [title]="getImageContainerTooltip()"
    (wheel)="onWheel($event)"
    (mousedown)="startPan($event)"
    (mousemove)="onPan($event)"
    (mouseup)="endPan()"
    (mouseleave)="endPan()"
  >
    <img
      #zoomImage
      class="zoom-image"
      [src]="getCurrentImage()?.fullUrl"
      [style.transform]="getImageTransform()"
      [style.transition]="isPanning ? 'none' : 'transform 0.3s ease'"
      (load)="onImageLoad()"
      (dragstart)="$event.preventDefault()"
      [alt]="getCurrentImage()?.alt || 'Zoomed Image'"
    />
  </div>

  <!-- Feature Hints -->
  <div class="feature-hints">
    <div class="hint" *ngIf="showZoomHintFlag">
      💡 Use mouse wheel or + button to zoom in
    </div>
    <div class="hint" *ngIf="showPanHintFlag">
      💡 Click and drag to pan around the image
    </div>
    <div class="hint" *ngIf="showNavigateHintFlag">
      💡 Use arrow keys or buttons to navigate between images
    </div>
  </div>
</div>
