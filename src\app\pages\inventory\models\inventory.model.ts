import { BannerListItem } from '@pages/administration/pages/public-page-config/pages/advertising/advertising.model';
import { Category } from "@pages/administration/pages/specification-configration/models/specification.model";
import { UnitImages } from "@pages/crm/models/customer-inventory.model";
import { GenericFilterParams, IdNameModel } from "src/app/@shared/models";
import { AssociationsUnits } from "./inventory-associations.model";

export class InventoryListGlobalFilter extends GenericFilterParams {
  archived = false;
}

export class InventoryListFilter {
  archived = false;
  orderBy!: string;
  direction = "asc";
  stockNumber?: string;
  year?: number;
  vin?: string;
  unitTypeIds?: number[];
  makeIds?: number[];
  unitModelIds?: number[];
  designation?: string;
  categoryId?: number;
  status?: string;
  ownerName?: string;
  createdByIds?: number[];
  startDate?: string;
  endDate?: string;
  purchaseStartDate?: string;
  purchaseEndDate?: string;
  isDisplayOnWeb?: boolean;
  salesPersonIds?: number[];
  currentLocationIds?: number[];
  receivingLocationIds?: number[];
  internetGroupIds?: number[];
  odometerReading?: number;
  retailAskingPrice?: number;
  initialInvestment?: number;
  actualInvestment?: number;
  totalProjectedInvestment?: number;
  duplicateSpecification: InventorySpecificationSearchDTO[][] = [];
  constructor() { }
}

export class InventorySpecificationSearchObj {
  constructor(public label: string, public value: string | number[], public type: string) { };
}

export class InventorySpecificationSearchDTO {
  searchKey = '';
  isString = false;
}

export class InventoryListItem {
  id!: number;
  archived!: boolean;
  isFinancialAdded!: boolean;
  createdBy!: IdNameModel;
  createdDate!: Date;
  generalInformation: InventoryGeneralInformation = new InventoryGeneralInformation();
  engineMake!: string;
  engineModel!: string;
  status?: boolean;
  unitImages?: UnitImages;
  unitAssociation?: UnitAssociation;
  unitAssociationId?: number;
  associations?: Array<AssociationsUnits>;
  unitStatus?: IdNameModel;
  unitSpecification?: InventoryUnitSpecificationModel;
  specificationDetails?: any;
  previousOwner!: PreviousOwner;
  unitAssociations!: Array<AssociationsUnits>;
  internetGroups?: InternetGroups[];
  internetOption?: InternetOption;
  unitLotLocation?: UnitLotLocation;
  odometerReading?: Number;
  financial?: Financial;
  salesPerson?: IdNameModel;
}

export class Financial {
  id!: number;
  unitId!: number;
  retailAskingPrice!: number;
  initialInvestment!: number;
  actualInvestment!: number;
  totalProjectedInvestment!: number;
  purchaseDate!: string;
}

export class InventoryUnitSpecificationModel {
  id!: number;
  unitId!: number;
  specificationData!: InventoryUnitSpecificationData;
}

export class InventoryUnitSpecificationData {
  specification!: InventoryListingSpecificationData[];
}
export class InventoryListingSpecificationData {
  id!: number;
  order!: number;
  fields!: SpecificationField[];
  isShow!: boolean;
  multiple!: boolean;
  sectionName!: string;
  parentName?: string;
}

export class SpecificationField {
  [key: string]: any;
  id!: number;
  for!: string;
  label!: string;
  value!: string | number;
  options!: IdNameModel[];
  dataType!: string;
  isRequired!: boolean;
  placeholder!: string;
  includePreference!: boolean;
}

export class InventoryGeneralInformation {
  designationId!: number;
  make!: IdNameModel;
  ownerId!: number;
  stockNumber!: string;
  unitModel!: Model;
  unitStatus!: IdNameModel;
  vin!: string;
  year!: number;
  unitType!: UnitType;
  vendorId!: number;
  id!: number;
  unitTypeCategory!: Category;
  owner!: Owner;
  designation!: Designation;
}

export interface Owner {
  id: number;
  name: string;
}
export interface Designation {
  id: number;
  name: string;
}
export interface UnitType {
  id: number;
  name: string;
  unitTypeCategoryId: number;
  unitTypeCategoryName: string;
  inventoryCount: number;
}

export class InventoryCreateParam {
  email!: string;
  phoneNumber!: string;
  employeeId!: string;
  firstName!: string;
  lastName!: string;
}

export interface Model {
  id: number;
  name: string;
  makeId: number;
}

export class PreviousOwner {
  id!: number;
  previousOwnerName!: string;
  previousOwnerContactId!: number;
  previousOwnerVendorId!: number;
  previousOwnerSupplierId!: number;
  type!: string;
  unitId!: number
}

export class InventoryGeneralDetails {
  id!: number;
  archived!: boolean;
  deleted!: boolean;
  generalInformation!: GeneralInformation;
  internetOption!: InternetOption;
  previousOwner!: PreviousOwner;
  internetGroups!: InternetGroups[];
  odometer!: Odometer;
  unitLotLocation!: UnitLotLocation;
  createdDate!: string;
  createdBy!: IdNameModel;
  unitAssociation?: UnitAssociation;
}

export class GeneralInformation {
  id!: number;
  year!: number;
  vin!: string;
  stockNumber!: string;
  designation!: IdNameModel;
  unitStatus!: IdNameModel;
  make!: IdNameModel;
  unitModel!: UnitModel;
  owner!: IdNameModel;
  unitType!: UnitType;
  vendor!: IdNameModel;
  unitTypeCategory!: IdNameModel;
  advertising!: BannerListItem;
  retailAskingPrice?: number;
}

export class UnitModel {
  id!: number;
  name!: string;
  makeId!: string;
}

export class UnitType {
  id!: number;
  name!: string;
  parentUnitTypeId!: string;
  inventoryCount!: number;
}

export class InternetOption {
  id!: number;
  displayOnWeb!: boolean;
  priceOnWeb!: boolean;
  unitId!: number;
}

export class Odometer {
  id!: number;
  odometerReading!: number;
  hours!: number;
  unitOfDistance!: IdNameModel;
  unitId!: number;
}

export class UnitLotLocation {
  id!: number;
  receivingLocation!: IdNameModel;
  currentLocation!: IdNameModel;
  unitId!: number;
}

export class IdName {
  id!: number;
  name!: string;
}

export class InternetGroups {
  id?: number;
  internetGroupId!: number;
  internetGroupName?: string;
  unitId?: number;
}

export class PipelineDetails {
  id!: number;
  pipelineType!: string;
  phases!: Phases[];
}

export class Phases {
  id!: number;
  sequenceNumber!: number;
  assignee!: IdNameModel;
  shop!: IdNameModel;
}

export enum ModelType {
  MAKE = 'make',
  MODEL = 'model',
  VENDOR = 'vendor',
  SUPPLIER = 'supplier',
  CONTACT = 'contact',
  PIPELINE = 'pipeline',
  UNIT_TYPE = 'unit-type',
  SALE = 'sale',
  ADVERTISING_CONFIG = 'advertising config',
  INTERNET_GROUP = 'internet group'
}

export interface GeneralInformationFormGroup {
  id: number | null;
  unitTypeId: number;
  year: number;
  vin: string;
  stockNumber: string;
  designationId: number;
  unitStatusId: number;
  makeId: number;
  unitModelId: number;
  ownerId: number;
  unitTypeCategoryId: number;
  advertisingId: number;
}

export interface InternetOptionFormGroup {
  id: number | null;
  displayOnWeb: boolean;
  priceOnWeb: boolean;
  unitId: number | null;
}

export interface PreviousOwnerFormGroup {
  id: number | null;
  previousOwnerName: number;
  previousOwnerContactId: number | null;
  previousOwnerVendorId: number | null;
  previousOwnerSupplierId: number | null;
  unitId: number | null;
  type: string;
}

export interface IdModel {
  id: number | null;
}

export interface OdometerReadingFormGroup {
  id: number | null;
  odometerReading: number;
  hours: number;
  unitOfDistance: IdModel;
  unitId: number | null;
}

export interface LotLocationFormGroup {
  id: number | null;
  receivingLocationId: number;
  currentLocationId: number;
  unitId: number | null;
}

export enum Tabs {
  GENERAL = 0,
  SPECIFICATION = 1,
  PHOTOS = 2,
  CONDITION = 3,
  NOTES = 4,
  DOCUMENTS = 5,
  ASSOCIATION = 6,
  COMMUNICATION = 7,
  SALEINFORMATION = 8,
  HOLD = 8,
  FINANCIAL = 9,
}

export interface ExcelData {
  Stock?: string;
  Year?: number;
  Make?: string;
  Model?: string;
  Image?: string;
  Status?: string;
  Active?: string;
  Category?: string;
  'Unit Type'?: string;
  Vin?: string;
  Designation?: string;
  Owner?: string;
  'Created By'?: string;
  'Creation Date'?: string | Date;
}

export interface UnitAssociation {
  name?: string;
  id?: string | null;
}
export interface SpecificationFieldsWithIndex {
  name: string;
  childIndex: number;
  parentIndex: number;
}

export interface InventoryStatus {
  id: number | string;
  name: string;
  inventoryCount: number;
}

export interface CreatedByUserCount {
  id: number | string;
  name: string;
  inventoryCount: number;
}

export interface FilterDataWithCounts {
  categories: IdNameCount[];
  makes: IdNameCount[];
  models: IdNameCount[];
  unitTypes: IdNameCount[];
  statuses: IdNameCount[];
  createdBy: IdNameCount[];
  salesPersons: IdNameCount[];
  currentLocation: IdNameCount[];
  receivingLocation: IdNameCount[];
  displayOnWeb: IdNameCount[];
}
 
export interface IdNameCount {
  id: number;
  name: string;
  count: number;
}