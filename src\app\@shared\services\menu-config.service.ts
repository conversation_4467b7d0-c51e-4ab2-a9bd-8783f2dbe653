import { Injectable } from '@angular/core';
import { ROUTER_UTILS } from '@core/utils';
import { MenuConfig } from '@core/utils/menu-config';
import { AuthService } from '@pages/auth/services/auth.service';
import { DropdownRouteName, MenuConfigModel, PermissionActions, PermissionModules } from '../models';
import { Utils } from './util.service';

const CUSTOMER_RELATIONSHIP_MANAGEMENT = 'Customer Relationship Management';
const INVENTORY = 'Inventory';
const SPECIFICATIONS_CONFIG = 'Specifications Config';
const PUBLIC_PAGE_CONFIG = 'Public Page Config';
const ADMINISTRATION = 'Administration';
const REPORTING = 'Reporting';

@Injectable({
  providedIn: 'root'
})
export class MenuConfigService {
  menuConfig = MenuConfig;
  menuConfigOriginal = JSON.parse(JSON.stringify(MenuConfig));
  menuChild = [
    {
      name: 'CR<PERSON>',
      children: [
        {
          name: 'Customer Lead',
          routerLink: [ROUTER_UTILS.config.crm.root, ROUTER_UTILS.config.crm.crmCustomer.root],
        },
        {
          name: 'Contacts',
          routerLink: [ROUTER_UTILS.config.crm.root, ROUTER_UTILS.config.crm.crmContact.root],
        },
        {
          name: 'Tasks',
          routerLink: [ROUTER_UTILS.config.crm.root, ROUTER_UTILS.config.crm.crmTask.root],
        }
      ]
    },
    {
      name: 'Transport',
      children: [
        {
          name: 'Incoming Truck Tracking',
          routerLink: [ROUTER_UTILS.config.transport.root, ROUTER_UTILS.config.transport.incomingTruckBoard.root],
        },
        {
          name: 'Driver Scheduling Board',
          routerLink: [ROUTER_UTILS.config.transport.root, ROUTER_UTILS.config.transport.driverScheduleBoard.root],
        }
      ]
    },

    // NOTE: The following modules are currently disabled per client's request.
    // {
    //   name: 'Pipeline',
    //   children: [
    //     {
    //       name: 'Sold Truck Board',
    //       routerLink: [ROUTER_UTILS.config.pipeline.root, ROUTER_UTILS.config.pipeline.soldTruckBoard.root],
    //     },
    //     {
    //       name: 'Stock Truck Board',
    //       routerLink: [ROUTER_UTILS.config.pipeline.root, ROUTER_UTILS.config.pipeline.stockTruckBoard.root],
    //     },
    //   ]
    // },
    {
      name: 'Reporting',
      children: [
        {
          name: 'Vendors',
          moduleKey: DropdownRouteName.VENDORS,
          routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.vendorsReport],
        },
        {
          name: 'Suppliers',
          moduleKey: DropdownRouteName.SUPPLIERS,
          routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.supplierReport],
        },
        {
          name: 'Customer Relationship Management',
          moduleKey: DropdownRouteName.CUSTOMER_RELATIONSHIP_MANAGEMENT,
          routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.crm.root],
          children: [
            {
              name: 'Daily Sales Report',
              moduleKey: DropdownRouteName.DAILY_SALES_REPORT,
              routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.crm.root, ROUTER_UTILS.config.reporting.crm.dailySalesReport.root],
            },
            {
              name: 'Activity Report',
              moduleKey: DropdownRouteName.ACTIVITY_REPORT,
              routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.crm.root, ROUTER_UTILS.config.reporting.crm.activity.root],
            },
            {
              name: 'Inventory Sales Report',
              moduleKey: DropdownRouteName.INVENTORY_SALES_REPORT,
              routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.crm.root, ROUTER_UTILS.config.reporting.crm.inventorySales.root],
            }
          ]
        },
        {
          name: 'Inventory',
          moduleKey: DropdownRouteName.INVENTORY_REPORTING,
          routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.inventoryReport.root],
          children: [
            {
              name: 'Profitability Report',
              moduleKey: DropdownRouteName.INVENTORY_PROFITABILITY,
              routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.inventoryReport.root, ROUTER_UTILS.config.reporting.inventoryReport.profitability.root],
            },
            {
              name: 'Inventory Aging Report',
              moduleKey: DropdownRouteName.INVENTORY_AGING,
              routerLink: [ROUTER_UTILS.config.reporting.root, ROUTER_UTILS.config.reporting.inventoryReport.root, ROUTER_UTILS.config.reporting.inventoryReport.inventoryAging.root],
            }
          ]
        },
      ]
    },
    {
      name: ADMINISTRATION,
      children: [
        {
          name: 'Users',
          routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.users.root],
        },
        {
          name: 'Dealers',
          routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.dealers.root],
        },
        {
          name: 'Vendors',
          routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.vendors.root],
        },
        {
          name: 'Suppliers',
          routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.suppliers.root],
        },
        // NOTE: The following modules are currently disabled per client's request.
        // {
        //   name: 'Pipeline Config',
        //   routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.pipelineConfig.root],
        // },
        // {
        //   name: 'Shops',
        //   routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.shops],
        // },
        {
          name: 'Roles & Permissions',
          routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.roles],
        },
        {
          name: 'Internet Groups Config',
          routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.internetGroupsConfig],
        },
        {
          name: PUBLIC_PAGE_CONFIG,
          moduleKey: DropdownRouteName.PUBLIC_PAGE_CONFIG,
          routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.publicPageConfig.root],
          children: [
            {
              name: 'Advertising Config',
              moduleKey: DropdownRouteName.ADVERTISING,
              routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.publicPageConfig.root, ROUTER_UTILS.config.administration.publicPageConfig.advertising.root],
            },
            {
              name: 'Quote Form Config',
              moduleKey: DropdownRouteName.QUOTE_FORM,
              routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.publicPageConfig.root, ROUTER_UTILS.config.administration.publicPageConfig.quoteForm.root],
            }
          ]
        },
        {
          name: SPECIFICATIONS_CONFIG,
          moduleKey: DropdownRouteName.SPECIFICATION_CONFIG,
          routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root],
          children: [
            {
              name: 'Category',
              moduleKey: DropdownRouteName.CATEGORY,
              routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root, ROUTER_UTILS.config.administration.specificationConfig.category.root],
            },
            {
              name: 'Unit Type',
              moduleKey: DropdownRouteName.UNITY_TYPE,
              routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root, ROUTER_UTILS.config.administration.specificationConfig.unitType.root],
            },
            {
              name: 'Make/Model',
              moduleKey: DropdownRouteName.MAKE_MODEL,
              routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root, ROUTER_UTILS.config.administration.specificationConfig.makeModel.root],
            },
            {
              name: 'Specification',
              moduleKey: DropdownRouteName.SPECIFICATION,
              routerLink: [ROUTER_UTILS.config.administration.root, ROUTER_UTILS.config.administration.specificationConfig.root, ROUTER_UTILS.config.administration.specificationConfig.specification.root],
            }
          ]
        },

      ]
    },
  ]

  constructor(
    private readonly authService: AuthService
  ) { }

  modifyMenuConfig(menuConfig: MenuConfigModel[]) {
    for (const [index, menu] of menuConfig.entries()) {
      const selectedMenu = this.menuChild.find(menuIem => menu.name === menuIem.name);
      menuConfig[index].children = selectedMenu?.children as any
    }

    const permissions = this.authService.getRoleInfo()?.privilegeActionResponseDTOs;
    const permissionActions = PermissionActions;
    const permissionModule = PermissionModules;

    const permissionsToHide = {
      'Administration': {
        'Users': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_USERS]),
        'Dealers': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_DEALERS]),
        'Vendors': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_VENDORS]),
        'Suppliers': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_VENDORS]),
        // NOTE: The following modules are currently disabled per client's request.
        // 'Pipeline Config': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_PIPELINE_CONFIG]),
        'Roles & Permissions': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_ROLE]),
        // NOTE: The following modules are currently disabled per client's request.
        // 'Shops': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_SHOPS]),
        'Public Page Config': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_ADVERTISE, permissionActions.VIEW_QUOTE_FORM]),
        'Specifications Config': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_CATEGORY, permissionActions.VIEW_UNIT_TYPE, permissionActions.VIEW_MAKE_MODEL, permissionActions.VIEW_SPECIFICATION_MASTER, permissionActions.VIEW_ROLE])
      },
      'Reporting': {
        'Vendors': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_VENDORS_REPORT]),
        'Suppliers': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_SUPPLIERS_REPORT]),
        'Customer Relationship Management': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_DAILY_SALES_REPORT, permissionActions.VIEW_ACTIVITY_REPORT, permissionActions.VIEW_INVENTORY_SALES_REPORT]),
        'Inventory': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_INVENTORY_AGING, permissionActions.VIEW_PROFITABILITY_REPORT]),
      },
      // NOTE: The following modules are currently disabled per client's request.
      // 'Pipeline': {
      //   'Sold Truck Board': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_SOLD_PIPELINE]),
      //   'Stock Truck Board': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_STOCK_PIPELINE])
      // },
      'Transport': {
        'Incoming Truck Tracking': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_INCOMING_TRUCK]),
        'Driver Scheduling Board': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_DRIVER_SCHEDULE])
      },
      'Dashboard': !Utils.hasModulePermission(permissions, permissionModule.DASHBOARD),
      'Inventory': !Utils.hasModulePermission(permissions, permissionModule.INVENTORY),
      // NOTE: The following modules are currently disabled per client's request.
      // 'Shops': !Utils.hasModulePermission(permissions, permissionModule.SHOP),
      'CRM': {
        'Customer Lead': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_CUSTOMER_LEAD]),
        'Contacts': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_CONTACTS]),
        'Tasks': !Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_SALES_TASK])
      }
    };

    function filterSectionByName(menuConfig: any[], sectionName: string): any[] {
      return menuConfig.filter(item => item.name !== sectionName);
    }

    function filterSubItemByName(children: any[], subItemName: string): any[] {
      return children.filter(item => item.name !== subItemName);
    }

    for (const sectionKey in permissionsToHide) {
      const section = menuConfig.find(item => item.name === sectionKey);
      if (section) {
        const subItemsToHide = (permissionsToHide as any)[sectionKey];
        if (typeof subItemsToHide !== 'object' && subItemsToHide) {
          menuConfig = filterSectionByName(menuConfig, sectionKey);
        }
        for (const subItemKey in subItemsToHide) {
          if (subItemsToHide[subItemKey]) {
            if (section.children) {
              section.children = filterSubItemByName(section.children, subItemKey);
            }
          }
        }
        if (section.children && section.children.length === 0) {
          menuConfig = filterSectionByName(menuConfig, sectionKey);
        }
      }
    }

    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_CATEGORY])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, ADMINISTRATION, SPECIFICATIONS_CONFIG, 'Category');
    }

    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_UNIT_TYPE])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, ADMINISTRATION, SPECIFICATIONS_CONFIG, 'Unit Type');
    }

    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_MAKE_MODEL])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, ADMINISTRATION, SPECIFICATIONS_CONFIG, 'Make/Model');
    }

    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_SPECIFICATION_MASTER])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, ADMINISTRATION, SPECIFICATIONS_CONFIG, 'Specification');
    }
    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_DAILY_SALES_REPORT])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, REPORTING, CUSTOMER_RELATIONSHIP_MANAGEMENT, 'Daily Sales Report');
    }
    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_ACTIVITY_REPORT])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, REPORTING, CUSTOMER_RELATIONSHIP_MANAGEMENT, 'Activity Report');
    }
    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_INVENTORY_SALES_REPORT])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, REPORTING, CUSTOMER_RELATIONSHIP_MANAGEMENT, 'Inventory Sales Report');
    }
    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_PROFITABILITY_REPORT])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, REPORTING, INVENTORY, 'Profitability Report');
    }
    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_INVENTORY_AGING])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, REPORTING, INVENTORY, 'Inventory Aging Report');
    }
    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_ADVERTISE])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, ADMINISTRATION, PUBLIC_PAGE_CONFIG, 'Advertising Config');
    }
    if (!Utils.hasSubModulePermission(permissions, [permissionActions.VIEW_QUOTE_FORM])) {
      menuConfig = this.hideSpecificationConfigMenu(menuConfig, ADMINISTRATION, PUBLIC_PAGE_CONFIG, 'Quote Form Config');
    }

    this.menuConfig = menuConfig;
  }

  hideModule(menuConfig: MenuConfigModel[], parentModule: string): MenuConfigModel[] {
    menuConfig = menuConfig.filter(
      menu => menu.name !== parentModule
    );

    return menuConfig;
  }

  hideSubModule(menuConfig: MenuConfigModel[], parentModule: string, childModuleName: string): MenuConfigModel[] {
    const mainMenuIndex = menuConfig.findIndex(menu => menu.name === parentModule);
    menuConfig[mainMenuIndex].children = menuConfig[mainMenuIndex]?.children?.filter(
      childMenu => childMenu.name !== childModuleName
    );

    return menuConfig;
  }

  hideSpecificationConfigMenu(menuConfig: MenuConfigModel[], mainModule: string, parentModule: string, childModuleName: string): MenuConfigModel[] {
    const mainMenuIndex = menuConfig.findIndex(menu => menu.name === mainModule);
    const mainMenuChildren = menuConfig[mainMenuIndex]?.children;
    if (mainMenuChildren?.length) {
      const parentMenuIndex = mainMenuChildren.findIndex(menu => menu.name === parentModule);
      if (mainMenuChildren[parentMenuIndex]?.children) {
        mainMenuChildren[parentMenuIndex].children = mainMenuChildren[parentMenuIndex]?.children?.filter(
          childMenu => childMenu.name !== childModuleName
        );
      }
      if ((menuConfig[mainMenuIndex] as any)?.children[parentMenuIndex]?.children) {
        (menuConfig[mainMenuIndex] as any).children[parentMenuIndex].children = mainMenuChildren[parentMenuIndex].children;
      }
    }

    return menuConfig;
  }

  getMenuConfig(): MenuConfigModel[] {
    return this.menuConfig;
  }

  resetMenuConfig(): void {
    this.menuConfig = this.menuConfigOriginal;
  }
}
